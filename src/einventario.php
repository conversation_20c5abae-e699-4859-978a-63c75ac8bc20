<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/inventario.php';
require_once __ROOT__ . '/src/classes/inventariocategoria.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_SESSION['idinventario'])) {
            $idinventario = $_SESSION['idinventario'];

            // logic:

            unset($_SESSION['idinventario']);
        } else {
            header('Location: rinventario');
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $idinventario = limpiar_datos($_POST['idinventario']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_mod
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_mod'])) {
    try {
        $modinventario = new Inventario();
        $modinventario->id = $idinventario;
        $modinventario->inventariocategoria = new InventarioCategoria();
        $modinventario->inventariocategoria->id = limpiar_datos($_POST['idinventariocategoria']);
        $modinventario->nombre = limpiar_datos($_POST['nombre']);
        $modinventario->estadoqty = limpiar_datos($_POST['estadoqty']);
        $modinventario->modify($conexion);

        header('Location: rinventario');

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_mod
#region sub_delinventario
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delinventario'])) {
    try {
        Inventario::delete($idinventario, $conexion);

        header('Location: rinventario?d=1');

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delinventario
#region try
try {
    $modinventario = Inventario::get($idinventario, $conexion);
    $categorias = InventarioCategoria::getList($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/einventario.view.php';

?>