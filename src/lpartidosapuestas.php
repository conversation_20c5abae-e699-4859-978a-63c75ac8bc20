<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';
require_once __ROOT__ . '/src/general/controltemplates.php';

//default
$totalprofit           = 0;
$porc_profit           = 0;
$param                 = array();
$team                  = '';
$id_cupon              = '';
$fecha                 = create_date();
$tabselected           = 1;
$sum_riesgo            = 0;
$focus_id_apuesta      = 0;
$solonoresuelto        = 0;
$solo_odds_por_revisar = 0;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'La apuesta ha sido modificada.';
        }
        if (isset($_SESSION['team'])) {
            $team = $_SESSION['team'];
            $id_cupon = $_SESSION['id_cupon'];
            $solonoresuelto = $_SESSION['solonoresuelto'];
            $fecha = $_SESSION['fecha_apuestas'];
        
            unset($_SESSION['team']);
            unset($_SESSION['id_cupon']);
            unset($_SESSION['solonoresuelto']);
            unset($_SESSION['fecha_apuestas']);
        }
        if(isset($_SESSION['focus_id_apuesta'])){
            $focus_id_apuesta = ordena($_SESSION['focus_id_apuesta']);

            unset($_SESSION['focus_id_apuesta']);
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $method_postsolo = 1;
        
        $team                  = limpiar_datos($_POST['team']);
        $id_cupon              = limpiar_datos($_POST['id_cupon']);
        $solonoresuelto        = @getvalue_checkbox($_POST['solonoresuelto']);
        $solo_odds_por_revisar = @getvalue_checkbox($_POST['solo_odds_por_revisar']);
        $fecha                 = limpiar_datos($_POST['fecha']);
        $tabselected           = limpiar_datos($_POST["tabselected"]);

        $_SESSION['team']           = $team;
        $_SESSION['id_cupon']       = $id_cupon;
        $_SESSION['solonoresuelto'] = $solonoresuelto;
        $_SESSION['fecha_apuestas'] = $fecha;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_editpartidoapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editpartidoapuesta'])) {
    try {
        $method_editpartidoapuesta = 1;

        $_SESSION['idpartidoapuesta'] = limpiar_datos($_POST['selidpartidoapuesta']);

        header('Location: espartidoapuesta');

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_editpartidoapuesta
#region sub_delpartido_checkapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delpartido_checkapuesta'])) {
    try {
        $method_sub_delpartido_checkapuesta = 1;
        
        $mdlidpartidoapuesta = limpiar_datos($_POST['mdl_checkapuesta_idpartidoapuesta']);

        PartidoApuesta::delete($mdlidpartidoapuesta, $conexion);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido eliminada.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delpartido_checkapuesta
#region sub_modpartido_checkapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_modpartido_checkapuesta'])) {
    try {
        $method_sub_modpartido_checkapuesta = 1;

        $_SESSION['idpartidoapuesta'] = limpiar_datos($_POST['mdl_checkapuesta_idpartidoapuesta']);

        header('Location: epartidoapuesta');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_modpartido_checkapuesta
#region sub_ganada_checkapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_ganada_checkapuesta'])) {
    try {
        $method_sub_ganada_checkapuesta = 1;

        $gn_idpartidoapuesta = limpiar_datos($_POST['mdl_checkapuesta_idpartidoapuesta']);

        $modpartidoapuesta = new PartidoApuesta;
        $modpartidoapuesta->id = $gn_idpartidoapuesta;
        $modpartidoapuesta->is_resuelto = 1;     
        $modpartidoapuesta->isganado = 1;     
        $modpartidoapuesta->modifyGanancia($conexion);

        $focus_id_apuesta = ordena($modpartidoapuesta->id);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido actualizada.';
        
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_ganada_checkapuesta
#region sub_perdida_checkapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_perdida_checkapuesta'])) {
    try {
        $method_sub_perdida_checkapuesta = 1;

        $gn_idpartidoapuesta = limpiar_datos($_POST['mdl_checkapuesta_idpartidoapuesta']);

        $modpartidoapuesta = new PartidoApuesta;
        $modpartidoapuesta->id = $gn_idpartidoapuesta;
        $modpartidoapuesta->is_resuelto = 1;     
        $modpartidoapuesta->isganado = 0;     
        $modpartidoapuesta->modifyGanancia($conexion);

        $focus_id_apuesta = ordena($modpartidoapuesta->id);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido actualizada.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_perdida_checkapuesta
#region sub_verinfo_checkapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_verinfo_checkapuesta'])) {
    try {
        $method_sub_verinfo_checkapuesta = 1;
        
        $_SESSION['idpartido'] = limpiar_datos($_POST['mdl_checkapuesta_idpartido']);

        header('Location: vpartidoinfo');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_verinfo_checkapuesta
#region sub_verapuesta_checkapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_verapuesta_checkapuesta'])) {
    try {
        $mdl_idpartidoapuesta = limpiar_datos($_POST['mdl_checkapuesta_idpartidoapuesta']);

        $modpartidoapuesta = PartidoApuesta::get($mdl_idpartidoapuesta, $conexion);

        $_SESSION['id_partido_apuesta'] = $modpartidoapuesta->id;
        $_SESSION['idpartido'] = $modpartidoapuesta->idpartido;
        $_SESSION['idtipoapuesta'] = $modpartidoapuesta->apuestatipo->id;
        $_SESSION['valorapuesta'] = $modpartidoapuesta->valorapuesta;
        $_SESSION['winprobabilityporc'] = $modpartidoapuesta->winprobabilityporc;

        header('Location: ipartidootrasapuestas');
        exit();

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_verapuesta_checkapuesta
#region try
try {
    $method_try = 1;

    $param['team']                  = $team;
    $param['id_cupon']              = $id_cupon;
    $param['solonoresuelto']        = $solonoresuelto;
    $param['solo_odds_por_revisar'] = $solo_odds_por_revisar;
    $param['fecha']                 = $fecha;
    $partidosapuestas = PartidoApuesta::getList($param, $conexion);
    $sum_riesgo       = PartidoApuesta::sumRiesgo($partidosapuestas);
    $actconfig        = Config::get($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lpartidosapuestas.view.php';

?>


