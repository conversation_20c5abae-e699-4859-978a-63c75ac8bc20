<?php

class Note {
	public $id_note;
	public $title;
	public $detail;
	public $source1title;
	public $source1ref;
	public $source2title;
	public $source2ref;
	public $tags;
	public $state;

	public static function get_list($search,$conexion) {
		try {
			$statement = $conexion->prepare(
				"SELECT * FROM notes WHERE state = 1 AND title LIKE ? OR tags LIKE ?"
			);
			$statement->bindValue(1, '%' . $search . '%', PDO::PARAM_STR);
			$statement->bindValue(2, '%' . $search . '%', PDO::PARAM_STR);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$notes = array();
	
				foreach ($resultados as $resultado) {
					$note = new Note;
					$note->id_note = $resultado['id_note'];
					$note->title = $resultado['title'];
					$note->detail = $resultado['detail'];
					$note->source1ref = $resultado['source1ref'];
					$note->source1title = $resultado['source1title'];
					$note->source2ref = $resultado['source2ref'];
					$note->source2title = $resultado['source2title'];
					$note->tags = $resultado['tags'];
					
					array_push($notes, $note);
				}
	
				return $notes;
			}
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	public static function get($id_note,$conexion) {		
		try {
			$statement = $conexion->prepare(
				'SELECT * FROM notes WHERE id_note = :id_note'
			);
			$statement->execute(array(
				':id_note' => $id_note
			));		
			$resultado = $statement->fetch();
		
			if ($resultado) {
				$note = new Note;
				$note->id_note = $resultado['id_note'];
				$note->title = $resultado['title'];
				$note->detail = html_entity_decode($resultado['detail']);
				$note->source1ref = $resultado['source1ref'];
				$note->source1title = $resultado['source1title'];
				$note->source2ref = $resultado['source2ref'];
				$note->source2title = $resultado['source2title'];
				$note->tags = $resultado['tags'];
	
				return $note;
	
			} else {
				return new Note;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}

	function add($conexion) {
		try {
			$statement = $conexion->prepare(
				'INSERT INTO notes (title, detail, source1ref, source1title, source2ref, source2title, tags)
				 VALUES (:title, :detail, :source1ref, :source1title, :source2ref, :source2title, :tags)'
			);
			$statement->execute(array(
				':title' => $this->title,
				':detail' => $this->detail,
				':source1ref' => $this->source1ref,
				':source1title' => $this->source1title,
				':source2ref' => $this->source2ref,				
				':source2title' => $this->source2title,				
				':tags' => $this->tags,				
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	function modify($conexion) {
		try {
			$statement = $conexion->prepare(
				'UPDATE notes SET title = :title, detail = :detail, source1ref = :source1ref, source1title = :source1title, source2ref = :source2ref, source2title = :source2title, tags = :tags
				 WHERE id_note = :id_note'
			);
			$statement->execute(array(
				':id_note' => $this->id_note,
				':title' => $this->title,
				':detail' => $this->detail,
				':source1ref' => $this->source1ref,
				':source1title' => $this->source1title,
				':source2ref' => $this->source2ref,				
				':source2title' => $this->source2title,				
				':tags' => $this->tags,				
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	public static function delete($id_note,$conexion) {
		try {
			$statement = $conexion->prepare(
				'UPDATE notes SET state = 0
				 WHERE id_note = :id_note'
			);
			$statement->execute(array(
				':id_note' => $id_note,
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>