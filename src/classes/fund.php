<?php

class Fund
{
    public string $id;
    public string $fecha;
    public string $banco;
    public string $persona;
    public float $valor_fund_usd;
    public float $total_cop_porusd;
    public float $total_fund_cop;
    public float $costo_usd;
    public float $trm;
    public float $costo_cop;
    public float $costo_iva;
    private string $bd_table = 'trad_funds';
    private string $bd_alias = 'trfu';
    private string $bd_id = 'id';
    private string $bd_fecha = 'fecha';
    private string $bd_banco = 'banco';
    private string $bd_persona = 'persona';
    private string $bd_valor_fund_usd = 'valor_fund_usd';
    private string $bd_total_cop_porusd = 'total_cop_porusd';
    private string $bd_total_fund_cop = 'total_fund_cop';
    private string $bd_costo_usd = 'costo_usd';
    private string $bd_trm = 'trm';
    private string $bd_costo_cop = 'costo_cop';
    private string $bd_costo_iva = 'costo_iva';

    function __construct()
    {
        $this->id               = '';
        $this->fecha            = '';
        $this->banco            = '';
        $this->persona          = '';
        $this->valor_fund_usd   = 0;
        $this->total_cop_porusd = 0;
        $this->total_fund_cop   = 0;
        $this->costo_usd        = 0;
        $this->trm              = 0;
        $this->costo_cop        = 0;
        $this->costo_iva        = 0;
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto                   = new self;
            $objeto->id               = desordena($resultado[$cq->bd_id]);
            $objeto->fecha            = $resultado[$cq->bd_fecha];
            $objeto->banco            = $resultado[$cq->bd_banco];
            $objeto->persona          = $resultado[$cq->bd_persona];
            $objeto->valor_fund_usd   = $resultado[$cq->bd_valor_fund_usd];
            $objeto->total_cop_porusd = $resultado[$cq->bd_total_cop_porusd];
            $objeto->total_fund_cop   = $resultado[$cq->bd_total_fund_cop];
            $objeto->costo_usd        = $resultado[$cq->bd_costo_usd];
            $objeto->trm              = $resultado[$cq->bd_trm];
            $objeto->costo_cop        = $resultado[$cq->bd_costo_cop];
            $objeto->costo_iva        = $resultado[$cq->bd_costo_iva];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, PDO $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_list(array $paramref, PDO $conexion): array
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id > 0 ";

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function agregar(PDO $conexion): void
    {
        try {
            $this->validar_data();

            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "   $cq->bd_fecha ";
            $query .= "  ,$cq->bd_banco ";
            $query .= "  ,$cq->bd_persona ";
            $query .= "  ,$cq->bd_valor_fund_usd ";
            $query .= "  ,$cq->bd_total_cop_porusd ";
            $query .= "  ,$cq->bd_total_fund_cop ";
            $query .= "  ,$cq->bd_costo_usd ";
            $query .= "  ,$cq->bd_trm ";
            $query .= "  ,$cq->bd_costo_cop ";
            $query .= "  ,$cq->bd_costo_iva ";
            $query .= ") VALUES (";
            $query .= "   :$cq->bd_fecha ";
            $query .= "  ,:$cq->bd_banco ";
            $query .= "  ,:$cq->bd_persona ";
            $query .= "  ,:$cq->bd_valor_fund_usd ";
            $query .= "  ,:$cq->bd_total_cop_porusd ";
            $query .= "  ,:$cq->bd_total_fund_cop ";
            $query .= "  ,:$cq->bd_costo_usd ";
            $query .= "  ,:$cq->bd_trm ";
            $query .= "  ,:$cq->bd_costo_cop ";
            $query .= "  ,:$cq->bd_costo_iva ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_fecha", $this->fecha);
            $statement->bindValue(":$cq->bd_banco", $this->banco);
            $statement->bindValue(":$cq->bd_persona", $this->persona);
            $statement->bindValue(":$cq->bd_valor_fund_usd", $this->valor_fund_usd);
            $statement->bindValue(":$cq->bd_total_cop_porusd", $this->total_cop_porusd);
            $statement->bindValue(":$cq->bd_total_fund_cop", $this->total_fund_cop);
            $statement->bindValue(":$cq->bd_costo_usd", $this->costo_usd);
            $statement->bindValue(":$cq->bd_trm", $this->trm);
            $statement->bindValue(":$cq->bd_costo_cop", $this->costo_cop);
            $statement->bindValue(":$cq->bd_costo_iva", $this->costo_iva);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modificar(PDO $conexion): void
    {
        try {
            $this->validar_data();

            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "   $cq->bd_fecha = :$cq->bd_fecha ";
            $query .= "  ,$cq->bd_banco = :$cq->bd_banco ";
            $query .= "  ,$cq->bd_persona = :$cq->bd_persona ";
            $query .= "  ,$cq->bd_valor_fund_usd = :$cq->bd_valor_fund_usd ";
            $query .= "  ,$cq->bd_total_cop_porusd = :$cq->bd_total_cop_porusd ";
            $query .= "  ,$cq->bd_total_fund_cop = :$cq->bd_total_fund_cop ";
            $query .= "  ,$cq->bd_costo_usd = :$cq->bd_costo_usd ";
            $query .= "  ,$cq->bd_trm = :$cq->bd_trm ";
            $query .= "  ,$cq->bd_costo_cop = :$cq->bd_costo_cop ";
            $query .= "  ,$cq->bd_costo_iva = :$cq->bd_costo_iva ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_fecha", $this->fecha);
            $statement->bindValue(":$cq->bd_banco", $this->banco);
            $statement->bindValue(":$cq->bd_persona", $this->persona);
            $statement->bindValue(":$cq->bd_valor_fund_usd", $this->valor_fund_usd);
            $statement->bindValue(":$cq->bd_total_cop_porusd", $this->total_cop_porusd);
            $statement->bindValue(":$cq->bd_total_fund_cop", $this->total_fund_cop);
            $statement->bindValue(":$cq->bd_costo_usd", $this->costo_usd);
            $statement->bindValue(":$cq->bd_trm", $this->trm);
            $statement->bindValue(":$cq->bd_costo_cop", $this->costo_cop);
            $statement->bindValue(":$cq->bd_costo_iva", $this->costo_iva);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function eliminar($id, PDO $conexion): void
    {
        try {
            $cq = new self;

            $query = " DELETE FROM $cq->bd_table ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validar_data(): void
    {
        try {
            $this->banco   = trim(mb_strtoupper($this->banco));
            $this->persona = trim(mb_strtoupper($this->persona));

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>