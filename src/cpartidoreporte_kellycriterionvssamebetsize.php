<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

$reporte = array();
$numsemana = getWeekActual();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;
        
        $actconfig = Config::get($conexion);
        $riesgo_samebetsize = $actconfig->valmin_valor_apuesta;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $method_postsolo = 1;
        
        $numsemana = limpiar_datos($_POST['num_semana']);
        $riesgo_samebetsize = limpiar_datos($_POST['riesgo_samebetsize']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_search
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_search'])) {
    try {
        $method_sub_search = 1;

        validar_textovacio($numsemana, "Debe especificar el numero de semana");
        validar_textovacio($riesgo_samebetsize, "Debe especificar el riesgo del metodo Same Bet Size.");

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_search
#region try
try {
    $method_try = 1;
    
    $semanas = Partido::getListNumWeek($conexion);
    $fechas = Partido::getListFechasPorSemana($numsemana, $conexion);
    $n = 0;

    for ($i=0; $i < count($fechas); $i++){
        $param['fecha'] = $fechas[$i];
        $param['solopersonal'] = 1;
        $param['soloresuelto'] = 1;
        $apuestas = PartidoApuesta::getListByFecha($param, $conexion);
        $recompensa_sbs_total = 0;
        $recompensa_total = 0;

        // SUMAR TODAS LAS APUESTAS DEL DIA PARA ENCONTRAR LA GANANCIA/PERDIDA DEL DIA PARA AMBOS METODOS.
        /** @var PartidoApuesta[] $apuestas */
        foreach ($apuestas as $apuesta) {            
            if($apuesta->isganado == 1){
                $recompensa = ($apuesta->riesgo * $apuesta->potencial) - $apuesta->riesgo;
                $recompensa_sbs = ($riesgo_samebetsize * $apuesta->potencial) - $riesgo_samebetsize;
            } else{
                $recompensa = -($apuesta->riesgo);
                $recompensa_sbs = -($riesgo_samebetsize);
            }

            $recompensa_total += $recompensa;
            $recompensa_sbs_total += $recompensa_sbs;
        }

        $reporte[$n]['fecha'] = $fechas[$i];
        $reporte[$n]['num_apuestas'] = count($apuestas);
        $reporte[$n]['recompensa_total'] = $recompensa_total;
        $reporte[$n]['recompensa_sbs_total'] = $recompensa_sbs_total;

        //Mostrar cual metodo es el mejor:
        if($recompensa_total > $recompensa_sbs_total){
            $reporte[$n]['better'] = 'KC';

        } elseif($recompensa_total == $recompensa_sbs_total){
            $reporte[$n]['better'] = 'N/A';
        } else{
            $reporte[$n]['better'] = 'SBS';
        }

        $n++; 
    }

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/cpartidoreporte_kellycriterionvssamebetsize.view.php';

?>