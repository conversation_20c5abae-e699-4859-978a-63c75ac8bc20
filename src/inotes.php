<?php global $conexion;
session_start();

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/note.php';
require_once __ROOT__ . '/src/general/preparar.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
	try {
		$title = limpiar_datos($_POST['title']);
		$detail = limpiar_datos($_POST['detail']);
		$tags = limpiar_datos($_POST['tags']);	
		
		validar_textovacio($title, 'Specify title');
		validar_textovacio($detail, 'Specify detail');

		$new_note = new Note;
		$new_note->title = strtolower($title);
		$new_note->detail = $detail;
		$new_note->source1ref = '';
		$new_note->source1title = '';
		$new_note->source2ref = '';
		$new_note->source2title = '';
		$new_note->tags = strtolower($tags);
		$new_note->add($conexion);
		
		header('Location: lnotes?add=1&search='.$new_note->title);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}

require_once __ROOT__ . '/views/inotes.view.php';

?>
