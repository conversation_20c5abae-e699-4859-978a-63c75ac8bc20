<?php session_start();

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/trkincome.php';
require_once __ROOT__ . '/src/general/preparar.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        $date = limpiar_datos($_POST['date']);
        $platform = limpiar_datos($_POST['platform']);
        $collected = limpiar_datos($_POST['collected']);

        validar_textovacio($date, 'Specify date.');
        validar_textovacio($platform, 'Specify platform.');
        validar_textovacio($collected, 'Specify collected.');

        $trkincome = new TrkIncome;
        $trkincome->date = $date;
        $trkincome->platform = $platform;
        $trkincome->collected = $collected;
        $trkincome->add($conexion);

        //limpiar
        $date = "";
        $platform = "";
        $collected = "";

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_tra'])) {
    try {
        $collected = limpiar_datos($_POST['tra_collected']);
        $trm = limpiar_datos($_POST['tra_trm']);
        $date = limpiar_datos($_POST['tra_date']);

        validar_textovacio($collected, 'Specify collected.');
        validar_textovacio($trm, 'Specify trm.');
        validar_textovacio($date, 'Specify date.');

        $trkincome = new TrkIncome;
        $trkincome->date = $date;
        $trkincome->trm = $trm;
        $trkincome->collected = $collected;
        $trkincome->add_transfer($conexion);

		$collected = "";
		$date = "";

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
} 

$trkincomes = TrkIncome::get_list(1,$conexion);
$trkincomeshistory = TrkIncome::get_list(0,$conexion);
$sumtranfer = get_sumtransfer($conexion);
$sumactive = TrkIncome::get_sumcollected($conexion);

function get_sumtransfer($conexion) {
    $month = get_month();
    $lastmonth = $month - 1;
    $year = get_year();
    
    $sumtransfer_thismonth = TrkIncome::get_sumtransfermonth($month, $year, $conexion);
    
    if ($month == 1) {
        $lastmonth == 12;
        $year -= 1;
    }
    
    $sumtransfer_lastmonth = TrkIncome::get_sumtransfermonth($lastmonth, $year, $conexion);

    return array(
        'sumtransfer_thismonth' => $sumtransfer_thismonth,
        'sumtransfer_lastmonth' => $sumtransfer_lastmonth
    );
}

require_once __ROOT__ . '/views/ltrkincomes.view.php';

?>