<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/deuda.php';
require_once __ROOT__ . '/src/classes/budget.php';
require_once __ROOT__ . '/src/classes/simulacion.php';
require_once __ROOT__ . '/src/general/preparar.php';

$tabselected = 2;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['i'])) {
            $success_display = 'show';
            $success_text = 'La deuda ha sido ingresada.';
        }
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'La deuda ha sido modificada.';
        }
        if (isset($_GET['p'])) {
            $success_display = 'show';
            $success_text = 'La deuda ha sido pagada.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $tabselected = limpiar_datos($_POST["tabselected"]);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_addsimulacion
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addsimulacion'])) {
    try {
        $newsimulacion = new Simulacion();
        $newsimulacion->nombre = limpiar_datos($_POST['nombresimulacion']);
        $newsimulacion->valor = limpiar_datos($_POST['valorsimulacion']);
        $newsimulacion->add($conexion);

        $newsimulacion = new Simulacion();

        $success_display = 'show';
        $success_text = 'La simulacion ha sido agregada.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_addsimulacion
#region sub_delsimulacion
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delsimulacion'])) {
    try {
        $delidsimulacion = limpiar_datos($_POST['mdl_delsimulacion_idsimulacion']);

        Simulacion::delete($delidsimulacion, $conexion);

        $success_display = 'show';
        $success_text = 'La simulacion ha sido eliminada.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delsimulacion
#region sub_delallsimulacion
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delallsimulacion'])) {
    try {
        Simulacion::deleteAll($conexion);

        $success_display = 'show';
        $success_text = 'Las simulaciones han sido eliminada.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delallsimulacion
#region try
try {
    $param = array();
    $param['includetrk'] = 1;
    $budgets = Budget::getList($param, $conexion);

    $disponible = Budget::getSumValor($budgets);

    $param = array();
    $param['disponible'] = $disponible;
    $param['essimulacion'] = 1;
    $param['dias'] = 7;
    $disponiblesxdias[] = Budget::calculateDisponiblePorDia($param, $conexion);
    $param['dias'] = 15;
    $disponiblesxdias[] = Budget::calculateDisponiblePorDia($param, $conexion);
    $param['dias'] = 30;
    $disponiblesxdias[] = Budget::calculateDisponiblePorDia($param, $conexion);

    $deudas = Deuda::getList(array(), $conexion);
    $valortotal = Deuda::getSumValor($deudas);

    $param = array();
    $param['deudas'] = $deudas;
    $param['essimulacion'] = 1;
    $deudas = Deuda::defineMoneyLeft($param, $conexion);

    $simulaciones = Simulacion::getList($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/sdeudas.view.php';

?>






















