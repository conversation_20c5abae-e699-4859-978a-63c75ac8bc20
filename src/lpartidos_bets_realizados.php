<?php 

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

global $conexion;

use App\classes\PartidoBet;
use App\classes\PartidoBetDetalle;
use App\classes\PartidoBetCriterio;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/apuestatipo.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        // Handle any GET parameters if needed
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'Operación completada exitosamente.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get

#region post
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Check if it's an AJAX request
        if (isset($_POST['ajax_check']) && $_POST['ajax_check'] == 1) {

            if (isset($_POST['action']) && $_POST['action'] == 'get_bet_criteria') {
                // Handle get bet criteria request
                $betId = $_POST['bet_id'] ?? '';

                // Validate bet ID
                validar_textovacio($betId, 'Debe especificar el ID de la apuesta');

                // Get all PartidoBetDetalle records for this bet
                $betDetails = PartidoBetDetalle::getByPartidoBet($betId, $conexion);

                if (empty($betDetails)) {
                    echo json_encode([
                        'status' => 'error',
                        'message' => 'No se encontraron detalles para esta apuesta'
                    ]);
                    exit;
                }

                // Build response data
                $responseData = [];

                foreach ($betDetails as $detail) {
                    $detailId = $detail->getId();

                    // Get criteria for this detail
                    $criteriaRecords = PartidoBetCriterio::getByPartidoBetDetalle($detailId, $conexion);

                    // Extract criteria data
                    $criteriaData = [];
                    if (!empty($criteriaRecords)) {
                        $criteriaRecord = $criteriaRecords[0]; // Should be only one record per detail

                        // Extract all 12 criteria fields
                        for ($i = 1; $i <= 12; $i++) {
                            $nombreMethod = "getCriterioNombre$i";
                            $valorMethod = "getCriterioValor$i";
                            $cumplidoMethod = "getCriterioCumplido$i";

                            $nombre = $criteriaRecord->$nombreMethod();
                            if (!empty($nombre)) {
                                $criteriaData[] = [
                                    'nombre' => $nombre,
                                    'valor' => $criteriaRecord->$valorMethod(),
                                    'cumplido' => $criteriaRecord->$cumplidoMethod()
                                ];
                            }
                        }
                    }

                    // Get match info from Partido
                    $partidoId = $detail->getIdPartido();
                    $partido = null;
                    $matchInfo = [
                        'home' => 'N/A',
                        'away' => 'N/A',
                        'fecha' => 'N/A',
                        'hora_militar' => '',
                        'pais' => 'N/A'
                    ];

                    if (!empty($partidoId)) {
                        $partido = Partido::get($partidoId, $conexion);
                        if ($partido) {
                            $matchInfo['home'] = $partido->home ?? 'N/A';
                            $matchInfo['away'] = $partido->away ?? 'N/A';
                            $matchInfo['fecha'] = $partido->fecha ?? 'N/A';
                            $matchInfo['hora_militar'] = $partido->horamilitar ?? '';
                            $matchInfo['pais'] = $partido->pais ?? 'N/A';
                        }
                    }

                    // Get bet type
                    $apuestaTipoId = $detail->getIdApuestaTipo();
                    $tipoApuesta = 'N/A';
                    if (!empty($apuestaTipoId)) {
                        $apuestaTipo = ApuestaTipo::get($apuestaTipoId, $conexion);
                        if ($apuestaTipo) {
                            $tipoApuesta = $apuestaTipo->nombre ?? 'N/A';
                        }
                    }

                    $responseData[] = [
                        'detail_id' => $detailId,
                        'match_info' => $matchInfo,
                        'tipo_apuesta' => $tipoApuesta,
                        'cuota' => $detail->getCuota(),
                        'probabilidad' => $detail->getProbabilidad(),
                        'is_cerrado' => $detail->getIsCerrado(),
                        'is_ganado' => $detail->getIsGanado(),
                        'criterios' => $criteriaData
                    ];
                }

                echo json_encode([
                    'status' => 'success',
                    'data' => $responseData
                ]);
                exit;
            }
        }

        // Handle date filter with session storage
        if (isset($_POST['fecha']) && !empty($_POST['fecha'])) {
            // Store the selected date in session for persistence
            $_SESSION['lpartidos_bets_realizados_fecha'] = $_POST['fecha'];
        }

        // Handle clear date filter request
        if (isset($_POST['clear_date_filter'])) {
            // Clear the session date filter
            unset($_SESSION['lpartidos_bets_realizados_fecha']);
        }
    } catch (Exception $e) {
        // Check if it's an AJAX request
        if (isset($_POST['ajax_check']) && $_POST['ajax_check'] == 1) {
            echo json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
            exit;
        }
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion post

#region try
try {
    // Set timezone to Bogotá for date filtering
    date_default_timezone_set('America/Bogota');

    // Prepare filters for data retrieval
    $filters = [];

    // Check for date filter with session persistence
    if (isset($_POST['fecha']) && !empty($_POST['fecha'])) {
        $filters['fecha'] = $_POST['fecha'];
    } elseif (isset($_SESSION['lpartidos_bets_realizados_fecha']) && !empty($_SESSION['lpartidos_bets_realizados_fecha'])) {
        // Use stored session date if available
        $filters['fecha'] = $_SESSION['lpartidos_bets_realizados_fecha'];
    }
    // If no date filter is set, show all records (don't set fecha in filters)

    // Always filter by active records
    $filters['estado'] = 1;

    // Retrieve betting history data
    $partidosBetsRealizados = PartidoBet::getListForBetsRealizados($conexion, $filters);

    // Retrieve profit/loss statistics
    $profitLossStats = PartidoBet::getProfitLossStatistics($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lpartidos_bets_realizados.view.php';

?>
