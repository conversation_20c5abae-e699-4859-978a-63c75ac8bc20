<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8" />
	<title>My Dash | Donation</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
	<meta content="" name="description" />
	<meta content="" name="author" />
	
	<!-- #head -->
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
</head>
<body>
	<!-- BEGIN #loader -->
	<div id="loader" class="app-loader">
		<span class="spinner"></span>
	</div>
	<!-- END #loader -->

	<!-- BEGIN #app -->
	<div id="app" class="app app-header-fixed app-sidebar-fixed">
		<!-- #header -->
		<?php require_once __ROOT__ . '/views/header.view.php'; ?>

		<!-- #sidebar -->
		<?php require_once __ROOT__ . '/views/sidebar.view.php'; ?>

		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<?php require_once __ROOT__ . '/views/labels.view.php'; ?>
			
			<!-- BEGIN page-header -->
			<h1 class="page-header">Donation</h1>
			<!-- END page-header -->
			
			<form action="acdbdonation" method="POST">
				<!-- BEGIN row -->
				<div class="row">
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px text-uppercase" name="period" id="period" value="<?php echo @recover_var($add_donation->period) ?>" autofocus/>
							<label for="period" class="d-flex align-items-center fs-15px">
								Period:
							</label>
						</div>
					</div>
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px" name="salary" id="salary" value="$<?php echo @recover_var(format_currency($add_donation->salary)) ?>" data-type="currency" onclick="this.focus();this.select()"/>
							<label for="salary" class="d-flex align-items-center fs-15px">
								Salary:
							</label>
						</div>
					</div>
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px" name="stamps" id="stamps" value="$<?php echo @recover_var(format_currency($add_donation->stamps)) ?>" data-type="currency" onclick="this.focus();this.select()"/>
							<label for="stamps" class="d-flex align-items-center fs-15px">
								Stamps:
							</label>
						</div>
					</div>
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px" name="socialsecurity" id="socialsecurity" value="$<?php echo @recover_var(format_currency($add_donation->socialsecurity)) ?>" data-type="currency" onclick="this.focus();this.select();"/>
							<label for="socialsecurity" class="d-flex align-items-center fs-15px">
								Social security:
							</label>
						</div>
					</div>
				</div>
				<!-- END row -->
				<!-- BEGIN row -->
				<div class="row mt-3">
					<div class="col">
						<button type="submit" id="sub_calc" name="sub_calc" class="btn btn-primary w-100">
							<i class="fa fa-calculator fa-lg fa-fw"></i>
						</button>
					</div>
				</div>
				<!-- END row -->
				<!-- BEGIN row -->
				<div class="row mt-3">
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px pointer" id="n_account" value="<?php echo @recover_var($n_account) ?>" readonly/>
							<label for="n_account" class="d-flex align-items-center fs-15px">
								# account:
							</label>
						</div>
					</div>
					<div class="col-1">
						<button type="button" 						
						data-toggle="clipboard" 
						data-clipboard-target="#n_account"
						class="btn btn-white btn-inline w-100"
						>
							<i class="fa fa-clipboard fa-lg fa-fw"></i>							
						</button>
					</div>
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px pointer" name="subtotal" id="subtotal" value="$<?php echo @recover_var(format_currency($add_donation->subtotal)) ?>" readonly/>
							<label for="subtotal" class="d-flex align-items-center fs-15px">
								Subtotal:
							</label>
						</div>
					</div>
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px pointer" name="todonate" id="todonate" value="$<?php echo @recover_var(format_currency($add_donation->todonate)) ?>" readonly/>
							<label for="todonate" class="d-flex align-items-center fs-15px">
								To donate:
							</label>
							<span id="todonateclean" style="position:absolute;opacity:0">
								<?php echo @recover_var($add_donation->todonate) ?>
							</span>
						</div>
					</div>
					<div class="col-1">
						<button type="button" 						
						data-toggle="clipboard" 
						data-clipboard-target="#todonateclean"
						class="btn btn-white btn-inline w-100"
						>
							<i class="fa fa-clipboard fa-lg fa-fw"></i>							
						</button>
					</div>
				</div>	
				<!-- END row -->
				<!-- BEGIN row -->
				<div class="row mt-3">
					<div class="col">
						<button type="submit" id="sub_add" name="sub_add" class="btn btn-success w-100">
							<i class="fa fa-arrow-down fa-lg fa-fw"></i>
						</button>
					</div>
				</div>
				<!-- END row -->
				<!-- BEGIN panel -->
				<div class="panel panel-inverse mt-3">
					<div class="panel-heading">
						<h4 class="panel-title">Donations</h4>
					</div>
					<!-- BEGIN panel-body -->
					<div class="panel-body">
						<table class="table table-hover table-sm">
							<thead>
								<tr>
									<th class="text-center">Period</th>
									<th class="text-center">Salary</th>
									<th class="text-center">Stamps</th>
									<th class="text-center">Social security</th>
									<th class="text-center">Subtotal</th>
									<th class="text-center">To donate</th>
								</tr>
							</thead>
							<tbody class="fs-14px">
								<?php foreach ($donations as $donation): ?>
									<tr>
										<td class="text-center"><?php echo limpiar_datos($donation->period); ?></td>
										<td class="text-end">$<?php echo format_currency(limpiar_datos($donation->salary)); ?></td>
										<td class="text-end">$<?php echo format_currency(limpiar_datos($donation->stamps)); ?></td>
										<td class="text-end">$<?php echo format_currency(limpiar_datos($donation->socialsecurity)); ?></td>
										<td class="text-end">$<?php echo format_currency(limpiar_datos($donation->subtotal)); ?></td>
										<td class="text-end text-danger">$<?php echo format_currency(limpiar_datos($donation->todonate)); ?></td>
									</tr>
								<?php endforeach; ?>
							</tbody>
						</table> 
					</div>
					<!-- END panel-body -->
				</div>
				<!-- END panel -->
			</form>
		</div>
		<!-- END #content -->
		
		<!-- BEGIN scroll-top-btn -->
		<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
		<!-- END scroll-top-btn -->
	</div>
	<!-- END #app -->

    <?php #region js ?>
    <!-- ================== BEGIN core-js ================== -->
    <script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
    <script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
    <!-- ================== END core-js ================== -->

    <script src="<?php echo htmlspecialchars(RUTA) ?>resources/js/fab.js"></script>
    <script src="<?php echo htmlspecialchars(RUTA) ?>resources/js/formatcurrency.js"></script>
    <script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/clipboard/dist/clipboard.min.js"></script>

    <script type="text/javascript">
        var clipboard = new ClipboardJS("[data-toggle='clipboard']");

        clipboard.on("success", function (e) {
            $(e.trigger).tooltip({
                title: "Copied",
                placement: "top"
            });
            $(e.trigger).tooltip("show");
            setTimeout(function () {
                $(e.trigger).tooltip("dispose");
            }, 500);
        });
    </script>
    <?php #endregion js ?>
</body>
</html>